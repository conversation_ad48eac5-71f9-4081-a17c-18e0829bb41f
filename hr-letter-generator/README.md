### hr-letter-generator

## Description

This project is designed to generate hr letters as pdfs for employees in Techcanopy.

## Installation

To install the project, follow these steps:

1. Clone the repository `https://github.com/techcanopysl/tsl-kernel-ext.git`.
2. Change branch to `feature/hr-letter-generator`.
3. Open directory `hr-letter-generator`.
4. Install dependencies with `npm install`.
5. Create a `.env` in main directory for environment variables
6. Add variables named `BASE_URL` , `ACCESS_TOKEN` and `EFFECTIVE_DATE`

# To generate Bonus Letters

open Terminal
command `npm run generate:bonus-letters csv_file_path uploads_path`
`csv_file_path` --> your absolute csv file path
`uploads_path` --> your absolute directory path, where you want to save your PDFs

# To generate Increment Letters

open Terminal
command `npm run generate:increment-letters csv_file_path uploads_path`
`csv_file_path` --> your absolute csv file path
`uploads_path` --> your absolute directory path, where you want to save your PDFs

Note:- sample files are available as `bonusLetter.csv` and `increments.csv` to insure the type of data. This data will show error while executing code. It is only for getting how your data will look like.

`node app.js "csvfilesinput/data.csv" "csvfiles"`

# Run by using docker container

step 1 ->

- create 3 folders,

  - csvfiles
  - csvfilesinput
  - csvfilesoutput

- add csv files in csvfilesinput
-

step 2 -> open terminal

- `docker compose build`
- `docker compose up -d`
- `docker ps` -- check the image is available update the same name of image in docker-compose.yml
- you will get container ID

step 2 ->

- `docker exec -it `first_3_chararter_of_container_ID` sh`

then use command `node app.js "csvfilesinput/incrementletters.csv" "csvfiles"` for increment letters

- generated pdfs will be available in csvfilesoutput

and `node appForBonusLetter.js "csvfilesinput/bonusletters.csv" "csvfiles"` for bonus letters

- generated pdfs will be available in csvfilesoutput
