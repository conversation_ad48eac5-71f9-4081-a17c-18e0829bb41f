import {
  addRequiredFields,
  allsalaryStructures,
  generateNewSalaryStructurePdf,
  salaryStructureAssignments,
} from "./utils/salaryStructure.js";
import { convertCsvToJson } from "./utils/convertCsvToJson.js";
import * as path from "path";
import { fileURLToPath } from "url";

const currentFileUrl = import.meta.url;
const currentFilePath = fileURLToPath(currentFileUrl);
const currentDirectoryPath = path.dirname(currentFilePath);
const filePath = process.argv[2];

const createPdfsByCsvFile = async () => {
  const csvfilepath = path.relative(
    path.resolve(currentDirectoryPath),
    filePath
  );
  try {
    const data = await convertCsvToJson(csvfilepath);
    console.log(`Received data from CSV file`, data);

    const salaryAssignments = await salaryStructureAssignments();
    const salaryStructures = await allsalaryStructures();
    console.log("salaryAssignments", salaryAssignments);
    console.log("salaryStructures", salaryStructures);

    let filteredDataBySalaryAssignment = data
      .filter((item) => {
        return salaryAssignments.find(
          (assignment) => assignment.employee === item.employeeId
        );
      })
      .map((item) => {
        const salaryAssignment = salaryAssignments.find(
          (assignment) => assignment.employee === item.employeeId
        );
        return { ...item, ...salaryAssignment };
      });
    if (filteredDataBySalaryAssignment.length === 0) {
      throw "No Employee is assigned for a salary structure";
    }
    filteredDataBySalaryAssignment = filteredDataBySalaryAssignment.map(
      (item) => {
        const salaryDetails = salaryStructures.find(
          (salary) => salary.name === item.salaryStructure
        );
        return { ...item, ...salaryDetails };
      }
    );

    const requiredData = filteredDataBySalaryAssignment.map((item) =>
      addRequiredFields(item)
    );
    console.log("requiredData for creating PDF", requiredData);

    // const processedData = await Promise.all(requiredData);
    // console.log("processedData for creating PDF", processedData);

    generateNewSalaryStructurePdf(requiredData);
  } catch (error) {
    console.log("Error:", error);
  }
};
createPdfsByCsvFile();

// node app.js "C:/Users/<USER>/Desktop/data.csv"

// node appForBonusLetter.js bonusLetters.csv
