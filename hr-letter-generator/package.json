{"name": "y", "version": "1.0.0", "description": "", "main": "app.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js", "generate:bonus-letters": "node appForBonusLetter.js", "generate:increment-letters": "node app.js"}, "author": "", "license": "ISC", "dependencies": {"axios": "^1.6.8", "csv-parser": "^3.0.0", "csvtojson": "^2.0.10", "dotenv": "^16.4.5", "express": "^4.18.3", "multer": "^1.4.5-lts.1", "nodemon": "^3.1.0", "typst": "^0.10.0-8", "winston": "^3.13.0", "winston-daily-rotate-file": "^5.0.0"}}