import { generateBonusLetterPdf } from "./utils/salaryStructure.js";
import { convertCsvToJson } from "./utils/convertCsvToJson.js";
import * as path from "path";
import { fileURLToPath } from "url";

const currentFileUrl = import.meta.url;
const currentFilePath = fileURLToPath(currentFileUrl);
const currentDirectoryPath = path.dirname(currentFilePath);
const filePath = process.argv[2];

const createPdfsByCsvFile = async () => {
  const csvfilepath = path.relative(
    path.resolve(currentDirectoryPath),
    filePath
  );
  try {
    let data = await convertCsvToJson(csvfilepath);

    if (data.length === 0) {
      throw "No Employee is assigned for a salary structure";
    }

    console.log("processedData for creating PDF", data);

    const pdfBuffer = generateBonusLetterPdf(data);
  } catch (error) {
    console.error("Error:", error);
  }
};
createPdfsByCsvFile();

// node appForBonusLetter.js bonusLetters.csv
