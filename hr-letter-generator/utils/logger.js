import { format, createLogger, transports } from "winston";
// import { DailyRotateFile } from "winston-daily-rotate-file";
import { config as dotenvConfig } from "dotenv";
import DailyRotateFile from "winston-daily-rotate-file";
const { combine, timestamp, json, printf, colorize } = format;

dotenvConfig();

const myFormat = printf(({ level, message, timestamp, ...rest }) => {
  return `${timestamp} [${level}] - ${message} ${JSON.stringify(
    rest,
    null,
    2
  )}`;
});

let fileFormat = {
  filename: "../logs/application-%DATE%.log",
  datePattern: "YYYY-MM-DD",
  zippedArchive: true,
  maxSize: "20m",
  maxFiles: "1d",
};

if (process.env.NODE_ENV !== "production") {
  fileFormat = { ...fileFormat, filename: "./logs/application-qa-%DATE%.log" };
}

// Configure daily rotate file transport
const fileRotateTransport = new DailyRotateFile(fileFormat);

const logger = createLogger({
  level: "info",
  format: combine(timestamp(), myFormat),
  transports: [fileRotateTransport],
});

if (process.env.NODE_ENV !== "production") {
  logger.add(new transports.Console({ format: myFormat }));
}

export default logger;
