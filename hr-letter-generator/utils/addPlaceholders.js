import fs from "fs";

export function addPlaceholdersAndSaveAsNew(
  templatePath,
  startingLines,
  newFilePath,
  callback
) {
  fs.readFile(templatePath, "utf8", (err, data) => {
    if (err) {
      return callback(err);
    }

    const modifiedContent = startingLines + data;

    fs.writeFile(newFilePath, modifiedContent, "utf8", (err) => {
      if (err) {
        return callback(err);
      }
      callback(null, `placeholders added and file saved as ${newFilePath}`);
    });
  });
}
