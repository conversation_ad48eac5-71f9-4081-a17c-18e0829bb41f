import axios from "axios";
import { addPlaceholdersAndSaveAsNew } from "./addPlaceholders.js";
import * as typst from "typst";
import fs from "fs";
import { createPlaceholders } from "./createPlaceholders.js";
1;
import * as dotenv from "dotenv";
dotenv.config();
const pdfUploadsPath = process.argv[3];

export const salaryStructure = async (string) => {
  try {
    const res = await axios.get(
      `${process.env.BASE_URL}/resource/Salary%20Structure/${string}`,
      {
        headers: {
          Authorization: `token ${process.env.ACCESS_TOKEN}`,
        },
      }
    );

    const { earnings, deductions, name } = res.data.data;

    const newEarnings = earnings.map((item) => {
      const { salary_component, formula } = item;
      return { salary_component, formula };
    });

    const newDeductions = deductions.map((item) => {
      const { salary_component, formula } = item;
      return { salary_component, formula };
    });

    return { name, earnings: newEarnings, deductions: newDeductions };
  } catch (err) {
    console.log(err);
    // throw err;
  }
};
export const allsalaryStructures = async () => {
  try {
    const res = await axios.get(
      `${process.env.BASE_URL}/resource/Salary%20Structure?limit_page_length=None`,
      {
        headers: {
          Authorization: `token ${process.env.ACCESS_TOKEN}`,
        },
      }
    );

    const structures = res.data.data;

    const response = structures.map(async (item) => {
      const structure = await salaryStructure(item.name);
      return structure;
    });

    return Promise.all(response);
  } catch (err) {
    console.log(err);
    // throw err;
  }
};

export const newSalaryStructure = async (data) => {
  try {
    const res = await axios.get(
      `${process.env.BASE_URL}/resource/Salary%20Structure%20Assignment?fields=["name","employee",%20"employee_name","designation","company","salary_structure",%20"base"%20,"variable"]&filters=[["employee", "=","${data.employeeId}"],["docstatus", "=","1"]]`,
      {
        headers: {
          Authorization: `token ${process.env.ACCESS_TOKEN}`,
        },
      }
    );

    if (res.data.data.length == 0) {
      throw `Salary structure Assigment not submitted for Employee ${data.employeeId}`;
    }
    return { ...data, ...res.data.data[0] };
  } catch (err) {
    console.log(err);
    // throw err;
  }
};

export const salaryStructureAssignments = async () => {
  try {
    const res = await axios.get(
      `${process.env.BASE_URL}/resource/Salary%20Structure%20Assignment?fields=["name","employee",%20"employee_name","designation","company","salary_structure",%20"base"%20,"variable"]&filters=[["docstatus", "=","1"]]&limit_page_length=None`,
      {
        headers: {
          Authorization: `token ${process.env.ACCESS_TOKEN}`,
        },
      }
    );
    return res.data.data;
  } catch (err) {
    console.log(err);
    // throw err;
  }
};

export const addRequiredFields = (employeeDetails) => {
  try {
    let totalCTC = Math.round(
      (employeeDetails.variable + employeeDetails.base) *
        ((100 + Number(employeeDetails.hike)) * 0.01)
    );

    // Remove % symbol and parse the percentage
    const variablePercentageNum = Number(employeeDetails.variablePercentage.replace('%', ''));

    let variable = Math.round(
      totalCTC * (variablePercentageNum * 0.01)
    );
    let base = Math.round(
      totalCTC * ((100 - variablePercentageNum) * 0.01)
    );

    let B;
    let HRA;
    let SA;
    let PF;
    let gratuity;

    employeeDetails.earnings.map((item) => {
      const { salary_component, formula } = item;
      switch (salary_component) {
        case "Basic":
          // Create a safe evaluation context with the base variable
          try {
            B = eval(formula.replace(/base/g, base));
          } catch (err) {
            console.log(`Error calculating Basic: ${err.message}`);
            B = NaN;
          }
          break;
        case "House Rent Allowance":
          try {
            HRA = eval(formula.replace(/base/g, base).replace(/B/g, B));
          } catch (err) {
            console.log(`Error calculating HRA: ${err.message}`);
            HRA = NaN;
          }
          break;
        case "Special Allowance":
          try {
            SA = eval(formula.replace(/base/g, base).replace(/B/g, B));
          } catch (err) {
            console.log(`Error calculating SA: ${err.message}`);
            SA = NaN;
          }
          break;
      }
    });
    employeeDetails.deductions.map((item) => {
      const { salary_component } = item;

      switch (salary_component) {
        case "Provident Fund":
          PF = B > 15000 ? 1800 : B * 0.12;
          break;
      }
    });
    gratuity = B * 0.0481;

    let basicAnnual = Math.round(B * 12);
    let hraAnnual = Math.round(HRA * 12);
    let saAnnual = Math.round(SA * 12);

    let gratuityAnnual = Math.round(gratuity * 12);
    let pfAnnual = Math.round(PF * 12);
    let selfLearning = 10000;

    let grossMonthly = Math.round(B + HRA + SA);
    let GrossAnnual = Math.round(basicAnnual + hraAnnual + saAnnual);

    let totalBenefits = Math.round(PF * 12 + gratuity * 12 + variable);

    let total = GrossAnnual + totalBenefits + selfLearning;
    let ctc = GrossAnnual + totalBenefits;
    let diff = Math.abs(base + variable - ctc);
    if (base + variable < ctc) {
      variable -= diff;
    } else if (base + variable < ctc) {
      variable += diff;
    }
    totalBenefits = Math.round(pfAnnual + gratuity * 12 + variable);
    total = GrossAnnual + totalBenefits + selfLearning;
    ctc = (GrossAnnual + totalBenefits + selfLearning).toLocaleString('en-IN');;
    let currentDate = new Date().toDateString();
    let effectiveDate = `${process.env.EFFECTIVE_DATE}`;
    B = Math.round(B);
    HRA = Math.round(HRA);
    SA = Math.round(SA);
    PF = Math.round(PF);
    gratuity = Math.round(gratuity);
    return {
      ...employeeDetails,
      base,
      variable,
      B,
      HRA,
      SA,
      PF,
      gratuity,
      basicAnnual,
      hraAnnual,
      saAnnual,
      gratuityAnnual,
      pfAnnual,
      selfLearning,
      grossMonthly,
      GrossAnnual,
      totalBenefits,
      total,
      ctc,
      currentDate,
      effectiveDate,
    };
  } catch (err) {
    console.log(err);
    // throw err;
  }
};

export const generateNewSalaryStructurePdf = (data) => {
  data.forEach((item) => {
    const templatePath = "./templates/incrementLetterTemplate.typ";

    const placeholders = createPlaceholders(item);

    const { employee_name, employee } = item;

    const newFilePath = `./uploads/typFile/${
      employee_name.split(" ")[0]
    }-${employee}.typ`; // Path where you want to save the new file

    addPlaceholdersAndSaveAsNew(
      templatePath,
      placeholders,
      newFilePath,
      async (err, message) => {
        try {
          if (err) {
            // console.error(`Error: ${err}`);
            console.log(err);
            // throw err;
          }
          // console.log(message);
          const pdfFilePath = `${pdfUploadsPath}/${
            employee_name.split(" ")[0]
          }-${employee}.pdf`;
          await typst.compile(newFilePath, pdfFilePath);
          console.log(`PDF created and file path ${pdfFilePath}`);
          fs.unlinkSync(newFilePath);
        } catch (err) {
          // console.error(`Error during compilation: ${error}`);
          console.log(err);
          // throw error;
        }
      }
    );
  });
};
export const generateBonusLetterPdf = (data) => {
  return data.map((item) => {
    const templatePath = "./templates/bonusLetterTemplate.typ";

    const placeholders = createPlaceholders(item);

    const { employee_name } = item;

    const newFilePath = `./uploads/typFile/${employee_name}-bonus-letter.typ`; // Path where you want to save the new file

    return addPlaceholdersAndSaveAsNew(
      templatePath,
      placeholders,
      newFilePath,
      async (err, message) => {
        try {
          if (err) {
            // console.error(`Error: ${err}`);
            console.log(err);
            // throw err;
          }
          // console.log(message);
          const pdfFilePath = `${pdfUploadsPath}/${employee_name}-bonus-letter.pdf`;
          await typst.compile(newFilePath, pdfFilePath);
          console.log(`PDF created and file path ${pdfFilePath}`);
          fs.unlinkSync(newFilePath);
        } catch (error) {
          // console.error(`Error during compilation: ${error}`);
          console.log(err);
          // throw error;
        }
      }
    );
  });
};
