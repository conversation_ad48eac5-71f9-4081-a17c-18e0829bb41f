export function indianNumberFormat(number) {
  if (isNaN(number) || number === 0) {
    return "0"; // Handle zero and non-numeric input
  }

  // Ensure number is positive (absolute value)
  number = Math.abs(number);

  // Convert to string and split into groups (including first group)
  const parts = number.toString().split(/(?=(\d{3})+)($|\.)/);

  // Reverse and join with commas
  return parts.reverse().join(",");
}
